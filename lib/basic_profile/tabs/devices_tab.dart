import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../flutter_flow/flutter_flow_radio_button.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../flutter_flow/flutter_flow_widgets.dart';
import '../../models/device.dart';
import '../../models/user.dart';
import '../../providers/app_provider.dart';
import '../services/profile_services.dart';

class DevicesTab extends StatefulWidget {
  const DevicesTab({Key? key}) : super(key: key);

  @override
  _DevicesTabState createState() => _DevicesTabState();
}

class _DevicesTabState extends State<DevicesTab> {
  String? dropDownValue;
  String? radioButtonValue3;
  TextEditingController? deviceNameController;
  TextEditingController? deviceNumberController;
  String radioButtonValue4 = '4G';
  bool? switchValue;
  String deviceMode = "new";
  Device? selectedDevice;

  @override
  void initState() {
    super.initState();
    deviceNameController = TextEditingController();
    deviceNumberController = TextEditingController();
  }

  @override
  void dispose() {
    deviceNameController?.dispose();
    deviceNumberController?.dispose();
    super.dispose();
  }

  void _resetForm() {
    setState(() {
      deviceMode = "new";
      selectedDevice = null;
      deviceNameController?.clear();
      deviceNumberController?.clear();
      dropDownValue = null;
      switchValue = null;
      radioButtonValue4 = '4G';
    });
  }

  void _editDevice(Device device) {
    setState(() {
      deviceMode = "edit";
      selectedDevice = device;
      deviceNameController?.text = device.deviceName;
      deviceNumberController?.text = device.deviceNumber;
      dropDownValue = device.uix;
      switchValue = device.isDefault;
      radioButtonValue4 = device.type.toUpperCase();
    });
  }

  @override
  Widget build(BuildContext context) {
    AppProvider authProvider = Provider.of<AppProvider>(context);
    User? user = authProvider.authClient;

    List<String>? devices = user?.devices
        ?.map((e) => e.deviceName != '' ? e.deviceName : e.deviceNumber)
        .toList();
    if (devices == null) {
      devices = [FFLocalizations.of(context).getText('eihwbyx6')].toList();
    } else {
      devices.insert(0, FFLocalizations.of(context).getText('eihwbyx6'));
    }

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0, 20, 0, 0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(24, 0, 0, 0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        'a03hzgid' /* Devices */,
                      ),
                      style: FlutterFlowTheme.of(context).bodyText1,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 0, 24, 0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        'eihwbyx6' /* Select Device */,
                      ),
                      style: FlutterFlowTheme.of(context).bodyText1,
                    ),
                  ),
                  Expanded(
                    child: FlutterFlowRadioButton(
                      options: devices,
                      initialValue: radioButtonValue3 ?? devices.first,
                      onChanged: (val) {
                        setState(() => radioButtonValue3 = val);
                        if (val != null &&
                            val !=
                                FFLocalizations.of(context)
                                    .getText('eihwbyx6')) {
                          Device? device = user?.devices?.firstWhere(
                            (element) =>
                                (element.deviceName != ''
                                    ? element.deviceName
                                    : element.deviceNumber) ==
                                val,
                          );
                          if (device != null) {
                            _editDevice(device);
                          }
                        } else {
                          _resetForm();
                        }
                      },
                      optionHeight: 32,
                      textStyle:
                          FlutterFlowTheme.of(context).bodyText1.override(
                                fontFamily: 'Roboto',
                                color: FlutterFlowTheme.of(context).primaryText,
                                fontWeight: FontWeight.normal,
                              ),
                      selectedTextStyle: FlutterFlowTheme.of(context)
                          .bodyText1
                          .override(
                            fontFamily: 'Roboto',
                            color: FlutterFlowTheme.of(context).secondaryColor,
                            fontWeight: FontWeight.normal,
                          ),
                      buttonPosition: RadioButtonPosition.left,
                      direction: Axis.vertical,
                      radioButtonColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      inactiveRadioButtonColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      toggleable: false,
                      horizontalAlignment: WrapAlignment.start,
                      verticalAlignment: WrapCrossAlignment.start,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
              child: TextFormField(
                controller: deviceNameController,
                obscureText: false,
                decoration: InputDecoration(
                  labelText: FFLocalizations.of(context).getText(
                    '4zruloja' /* Device Name */,
                  ),
                  hintText: FFLocalizations.of(context).getText(
                    '6rtja7rs' /* Input device alias */,
                  ),
                  hintStyle: FlutterFlowTheme.of(context).bodyText2,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Roboto',
                      fontWeight: FontWeight.normal,
                    ),
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
              child: TextFormField(
                controller: deviceNumberController,
                obscureText: false,
                decoration: InputDecoration(
                  labelText: FFLocalizations.of(context).getText(
                    'sfdx8clw' /* Device Number */,
                  ),
                  hintText: FFLocalizations.of(context).getText(
                    '5fu32vdy' /* Input device number */,
                  ),
                  hintStyle: FlutterFlowTheme.of(context).bodyText2,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0x00000000),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Roboto',
                      fontWeight: FontWeight.normal,
                    ),
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0, 0, 24, 0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        'wue84sbf' /* UIX */,
                      ),
                      style: FlutterFlowTheme.of(context).bodyText1,
                    ),
                  ),
                  Expanded(
                    child: FlutterFlowRadioButton(
                      options: [
                        'CarV1.2',
                        'Asa 1.2',
                      ],
                      initialValue: dropDownValue ?? 'CarV1.2',
                      onChanged: (val) => setState(() => dropDownValue = val),
                      optionHeight: 32,
                      textStyle:
                          FlutterFlowTheme.of(context).bodyText1.override(
                                fontFamily: 'Roboto',
                                color: FlutterFlowTheme.of(context).primaryText,
                                fontWeight: FontWeight.normal,
                              ),
                      selectedTextStyle: FlutterFlowTheme.of(context)
                          .bodyText1
                          .override(
                            fontFamily: 'Roboto',
                            color: FlutterFlowTheme.of(context).secondaryColor,
                            fontWeight: FontWeight.normal,
                          ),
                      buttonPosition: RadioButtonPosition.left,
                      direction: Axis.vertical,
                      radioButtonColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      inactiveRadioButtonColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      toggleable: false,
                      horizontalAlignment: WrapAlignment.start,
                      verticalAlignment: WrapCrossAlignment.start,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      'dnfuzw8y' /* Is Default */,
                    ),
                    style: FlutterFlowTheme.of(context).bodyText1,
                  ),
                  Switch(
                    value: switchValue ??= false,
                    onChanged: (newValue) async {
                      setState(() => switchValue = newValue);
                    },
                    activeColor: FlutterFlowTheme.of(context).secondaryColor,
                    activeTrackColor: FlutterFlowTheme.of(context).primaryColor,
                    inactiveTrackColor:
                        FlutterFlowTheme.of(context).primaryColor,
                    inactiveThumbColor:
                        FlutterFlowTheme.of(context).secondaryText,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 20, 24, 20),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0, 0, 8, 0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          if (user?.role != 'guest') {
                            await ProfileServices.saveDevice(
                              context: context,
                              authProvider: authProvider,
                              deviceName: deviceNameController!.text,
                              deviceNumber: deviceNumberController!.text,
                              uix: dropDownValue,
                              isDefault: switchValue ?? false,
                              deviceType: radioButtonValue4,
                              deviceMode: deviceMode,
                              selectedDevice: selectedDevice,
                            );
                          }
                        },
                        text: FFLocalizations.of(context).getText(
                          '8o9d116q' /* Save */,
                        ),
                        options: FFButtonOptions(
                          width: 130,
                          height: 40,
                          color: FlutterFlowTheme.of(context).secondaryColor,
                          textStyle: FlutterFlowTheme.of(context)
                              .subtitle2
                              .override(
                                fontFamily: 'Roboto',
                                color: FlutterFlowTheme.of(context).primaryText,
                                fontWeight: FontWeight.w500,
                              ),
                          borderSide: BorderSide(
                            color: Colors.transparent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          if (user?.role != 'guest' && selectedDevice != null) {
                            await ProfileServices.deleteDevice(
                              context: context,
                              authProvider: authProvider,
                              selectedDevice: selectedDevice,
                            );
                            _resetForm();
                          }
                        },
                        text: FFLocalizations.of(context).getText(
                          '6uu4xjgr' /* Delete */,
                        ),
                        options: FFButtonOptions(
                          width: 130,
                          height: 40,
                          color: FlutterFlowTheme.of(context).alternate,
                          textStyle: FlutterFlowTheme.of(context)
                              .subtitle2
                              .override(
                                fontFamily: 'Roboto',
                                color:
                                    FlutterFlowTheme.of(context).primaryBtnText,
                                fontWeight: FontWeight.w500,
                              ),
                          borderSide: BorderSide(
                            color: Colors.transparent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
